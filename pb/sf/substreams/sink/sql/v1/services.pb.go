// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.27.0
// 	protoc        (unknown)
// source: sf/substreams/sink/sql/v1/services.proto

package pbsql

import (
	_ "github.com/streamingfast/substreams/pb/sf/substreams"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Service_Engine int32

const (
	Service_unset      Service_Engine = 0
	Service_postgres   Service_Engine = 1
	Service_clickhouse Service_Engine = 2
)

// Enum value maps for Service_Engine.
var (
	Service_Engine_name = map[int32]string{
		0: "unset",
		1: "postgres",
		2: "clickhouse",
	}
	Service_Engine_value = map[string]int32{
		"unset":      0,
		"postgres":   1,
		"clickhouse": 2,
	}
)

func (x Service_Engine) Enum() *Service_Engine {
	p := new(Service_Engine)
	*p = x
	return p
}

func (x Service_Engine) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service_Engine) Descriptor() protoreflect.EnumDescriptor {
	return file_sf_substreams_sink_sql_v1_services_proto_enumTypes[0].Descriptor()
}

func (Service_Engine) Type() protoreflect.EnumType {
	return &file_sf_substreams_sink_sql_v1_services_proto_enumTypes[0]
}

func (x Service_Engine) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service_Engine.Descriptor instead.
func (Service_Engine) EnumDescriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{0, 0}
}

type Service struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Containing both create table statements and index creation statements.
	Schema               string                `protobuf:"bytes,1,opt,name=schema,proto3" json:"schema,omitempty"`
	DbtConfig            *DBTConfig            `protobuf:"bytes,2,opt,name=dbt_config,json=dbtConfig,proto3,oneof" json:"dbt_config,omitempty"`
	HasuraFrontend       *HasuraFrontend       `protobuf:"bytes,4,opt,name=hasura_frontend,json=hasuraFrontend,proto3" json:"hasura_frontend,omitempty"`
	PostgraphileFrontend *PostgraphileFrontend `protobuf:"bytes,5,opt,name=postgraphile_frontend,json=postgraphileFrontend,proto3" json:"postgraphile_frontend,omitempty"`
	Engine               Service_Engine        `protobuf:"varint,7,opt,name=engine,proto3,enum=sf.substreams.sink.sql.v1.Service_Engine" json:"engine,omitempty"`
	RestFrontend         *RESTFrontend         `protobuf:"bytes,8,opt,name=rest_frontend,json=restFrontend,proto3" json:"rest_frontend,omitempty"`
}

func (x *Service) Reset() {
	*x = Service{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *Service) GetDbtConfig() *DBTConfig {
	if x != nil {
		return x.DbtConfig
	}
	return nil
}

func (x *Service) GetHasuraFrontend() *HasuraFrontend {
	if x != nil {
		return x.HasuraFrontend
	}
	return nil
}

func (x *Service) GetPostgraphileFrontend() *PostgraphileFrontend {
	if x != nil {
		return x.PostgraphileFrontend
	}
	return nil
}

func (x *Service) GetEngine() Service_Engine {
	if x != nil {
		return x.Engine
	}
	return Service_unset
}

func (x *Service) GetRestFrontend() *RESTFrontend {
	if x != nil {
		return x.RestFrontend
	}
	return nil
}

// https://www.getdbt.com/product/what-is-dbt
type DBTConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files              []byte `protobuf:"bytes,1,opt,name=files,proto3" json:"files,omitempty"`
	RunIntervalSeconds int32  `protobuf:"varint,2,opt,name=run_interval_seconds,json=runIntervalSeconds,proto3" json:"run_interval_seconds,omitempty"`
	Enabled            bool   `protobuf:"varint,3,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *DBTConfig) Reset() {
	*x = DBTConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DBTConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBTConfig) ProtoMessage() {}

func (x *DBTConfig) ProtoReflect() protoreflect.Message {
	mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBTConfig.ProtoReflect.Descriptor instead.
func (*DBTConfig) Descriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{1}
}

func (x *DBTConfig) GetFiles() []byte {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *DBTConfig) GetRunIntervalSeconds() int32 {
	if x != nil {
		return x.RunIntervalSeconds
	}
	return 0
}

func (x *DBTConfig) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// https://hasura.io/docs/latest/index/
type HasuraFrontend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *HasuraFrontend) Reset() {
	*x = HasuraFrontend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HasuraFrontend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HasuraFrontend) ProtoMessage() {}

func (x *HasuraFrontend) ProtoReflect() protoreflect.Message {
	mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HasuraFrontend.ProtoReflect.Descriptor instead.
func (*HasuraFrontend) Descriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{2}
}

func (x *HasuraFrontend) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// https://www.graphile.org/postgraphile/
type PostgraphileFrontend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *PostgraphileFrontend) Reset() {
	*x = PostgraphileFrontend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostgraphileFrontend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostgraphileFrontend) ProtoMessage() {}

func (x *PostgraphileFrontend) ProtoReflect() protoreflect.Message {
	mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostgraphileFrontend.ProtoReflect.Descriptor instead.
func (*PostgraphileFrontend) Descriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{3}
}

func (x *PostgraphileFrontend) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// https://github.com/sosedoff/pgweb
type PGWebFrontend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *PGWebFrontend) Reset() {
	*x = PGWebFrontend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PGWebFrontend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PGWebFrontend) ProtoMessage() {}

func (x *PGWebFrontend) ProtoReflect() protoreflect.Message {
	mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PGWebFrontend.ProtoReflect.Descriptor instead.
func (*PGWebFrontend) Descriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{4}
}

func (x *PGWebFrontend) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

// https://github.com/semiotic-ai/sql-wrapper
type RESTFrontend struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *RESTFrontend) Reset() {
	*x = RESTFrontend{}
	if protoimpl.UnsafeEnabled {
		mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RESTFrontend) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RESTFrontend) ProtoMessage() {}

func (x *RESTFrontend) ProtoReflect() protoreflect.Message {
	mi := &file_sf_substreams_sink_sql_v1_services_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RESTFrontend.ProtoReflect.Descriptor instead.
func (*RESTFrontend) Descriptor() ([]byte, []int) {
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP(), []int{5}
}

func (x *RESTFrontend) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

var File_sf_substreams_sink_sql_v1_services_proto protoreflect.FileDescriptor

var file_sf_substreams_sink_sql_v1_services_proto_rawDesc = []byte{
	0x0a, 0x28, 0x73, 0x66, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x2f,
	0x73, 0x69, 0x6e, 0x6b, 0x2f, 0x73, 0x71, 0x6c, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x73, 0x66, 0x2e, 0x73,
	0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x2e, 0x73, 0x69, 0x6e, 0x6b, 0x2e, 0x73,
	0x71, 0x6c, 0x2e, 0x76, 0x31, 0x1a, 0x1b, 0x73, 0x66, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x73, 0x2f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x80, 0x04, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e,
	0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x06,
	0xc2, 0x89, 0x01, 0x02, 0x08, 0x01, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x48,
	0x0a, 0x0a, 0x64, 0x62, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x73, 0x66, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x73, 0x2e, 0x73, 0x69, 0x6e, 0x6b, 0x2e, 0x73, 0x71, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x42, 0x54, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x64, 0x62, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x88, 0x01, 0x01, 0x12, 0x52, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x75,
	0x72, 0x61, 0x5f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x73, 0x66, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x73, 0x2e, 0x73, 0x69, 0x6e, 0x6b, 0x2e, 0x73, 0x71, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x61,
	0x73, 0x75, 0x72, 0x61, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x0e, 0x68, 0x61,
	0x73, 0x75, 0x72, 0x61, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x64, 0x0a, 0x15,
	0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x66,
	0x2e, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x2e, 0x73, 0x69, 0x6e, 0x6b,
	0x2e, 0x73, 0x71, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x61, 0x70,
	0x68, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x14, 0x70, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x61, 0x70, 0x68, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x29, 0x2e, 0x73, 0x66, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x73, 0x2e, 0x73, 0x69, 0x6e, 0x6b, 0x2e, 0x73, 0x71, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x06, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x4c, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x73,
	0x66, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x2e, 0x73, 0x69, 0x6e,
	0x6b, 0x2e, 0x73, 0x71, 0x6c, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x45, 0x53, 0x54, 0x46, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x74, 0x46, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x22, 0x31, 0x0a, 0x06, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x09, 0x0a,
	0x05, 0x75, 0x6e, 0x73, 0x65, 0x74, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x73, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x10, 0x02, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x64, 0x62, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x75, 0x0a, 0x09, 0x44, 0x42, 0x54, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x1c, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x42, 0x06, 0xc2, 0x89, 0x01, 0x02, 0x10, 0x01, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x30, 0x0a, 0x14, 0x72, 0x75, 0x6e, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x72, 0x75, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x2a, 0x0a, 0x0e,
	0x48, 0x61, 0x73, 0x75, 0x72, 0x61, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x30, 0x0a, 0x14, 0x50, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x61, 0x70, 0x68, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x29, 0x0a, 0x0d, 0x50, 0x47,
	0x57, 0x65, 0x62, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x28, 0x0a, 0x0c, 0x52, 0x45, 0x53, 0x54, 0x46, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x42,
	0xee, 0x01, 0x0a, 0x1d, 0x63, 0x6f, 0x6d, 0x2e, 0x73, 0x66, 0x2e, 0x73, 0x75, 0x62, 0x73, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x73, 0x2e, 0x73, 0x69, 0x6e, 0x6b, 0x2e, 0x73, 0x71, 0x6c, 0x2e, 0x76,
	0x31, 0x42, 0x0d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x50, 0x01, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x66, 0x61, 0x73, 0x74, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x2d, 0x73, 0x69, 0x6e, 0x6b, 0x2d, 0x73, 0x71, 0x6c,
	0x2f, 0x70, 0x62, 0x3b, 0x70, 0x62, 0x73, 0x71, 0x6c, 0xa2, 0x02, 0x04, 0x53, 0x53, 0x53, 0x53,
	0xaa, 0x02, 0x19, 0x53, 0x66, 0x2e, 0x53, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73,
	0x2e, 0x53, 0x69, 0x6e, 0x6b, 0x2e, 0x53, 0x71, 0x6c, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x19, 0x53,
	0x66, 0x5c, 0x53, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x5c, 0x53, 0x69, 0x6e,
	0x6b, 0x5c, 0x53, 0x71, 0x6c, 0x5c, 0x56, 0x31, 0xe2, 0x02, 0x25, 0x53, 0x66, 0x5c, 0x53, 0x75,
	0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x73, 0x5c, 0x53, 0x69, 0x6e, 0x6b, 0x5c, 0x53, 0x71,
	0x6c, 0x5c, 0x56, 0x31, 0x5c, 0x47, 0x50, 0x42, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0xea, 0x02, 0x1d, 0x53, 0x66, 0x3a, 0x3a, 0x53, 0x75, 0x62, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x73, 0x3a, 0x3a, 0x53, 0x69, 0x6e, 0x6b, 0x3a, 0x3a, 0x53, 0x71, 0x6c, 0x3a, 0x3a, 0x56, 0x31,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sf_substreams_sink_sql_v1_services_proto_rawDescOnce sync.Once
	file_sf_substreams_sink_sql_v1_services_proto_rawDescData = file_sf_substreams_sink_sql_v1_services_proto_rawDesc
)

func file_sf_substreams_sink_sql_v1_services_proto_rawDescGZIP() []byte {
	file_sf_substreams_sink_sql_v1_services_proto_rawDescOnce.Do(func() {
		file_sf_substreams_sink_sql_v1_services_proto_rawDescData = protoimpl.X.CompressGZIP(file_sf_substreams_sink_sql_v1_services_proto_rawDescData)
	})
	return file_sf_substreams_sink_sql_v1_services_proto_rawDescData
}

var file_sf_substreams_sink_sql_v1_services_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_sf_substreams_sink_sql_v1_services_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_sf_substreams_sink_sql_v1_services_proto_goTypes = []interface{}{
	(Service_Engine)(0),          // 0: sf.substreams.sink.sql.v1.Service.Engine
	(*Service)(nil),              // 1: sf.substreams.sink.sql.v1.Service
	(*DBTConfig)(nil),            // 2: sf.substreams.sink.sql.v1.DBTConfig
	(*HasuraFrontend)(nil),       // 3: sf.substreams.sink.sql.v1.HasuraFrontend
	(*PostgraphileFrontend)(nil), // 4: sf.substreams.sink.sql.v1.PostgraphileFrontend
	(*PGWebFrontend)(nil),        // 5: sf.substreams.sink.sql.v1.PGWebFrontend
	(*RESTFrontend)(nil),         // 6: sf.substreams.sink.sql.v1.RESTFrontend
}
var file_sf_substreams_sink_sql_v1_services_proto_depIdxs = []int32{
	2, // 0: sf.substreams.sink.sql.v1.Service.dbt_config:type_name -> sf.substreams.sink.sql.v1.DBTConfig
	3, // 1: sf.substreams.sink.sql.v1.Service.hasura_frontend:type_name -> sf.substreams.sink.sql.v1.HasuraFrontend
	4, // 2: sf.substreams.sink.sql.v1.Service.postgraphile_frontend:type_name -> sf.substreams.sink.sql.v1.PostgraphileFrontend
	0, // 3: sf.substreams.sink.sql.v1.Service.engine:type_name -> sf.substreams.sink.sql.v1.Service.Engine
	6, // 4: sf.substreams.sink.sql.v1.Service.rest_frontend:type_name -> sf.substreams.sink.sql.v1.RESTFrontend
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_sf_substreams_sink_sql_v1_services_proto_init() }
func file_sf_substreams_sink_sql_v1_services_proto_init() {
	if File_sf_substreams_sink_sql_v1_services_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_sf_substreams_sink_sql_v1_services_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Service); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sf_substreams_sink_sql_v1_services_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DBTConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sf_substreams_sink_sql_v1_services_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HasuraFrontend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sf_substreams_sink_sql_v1_services_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostgraphileFrontend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sf_substreams_sink_sql_v1_services_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PGWebFrontend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_sf_substreams_sink_sql_v1_services_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RESTFrontend); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_sf_substreams_sink_sql_v1_services_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sf_substreams_sink_sql_v1_services_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_sf_substreams_sink_sql_v1_services_proto_goTypes,
		DependencyIndexes: file_sf_substreams_sink_sql_v1_services_proto_depIdxs,
		EnumInfos:         file_sf_substreams_sink_sql_v1_services_proto_enumTypes,
		MessageInfos:      file_sf_substreams_sink_sql_v1_services_proto_msgTypes,
	}.Build()
	File_sf_substreams_sink_sql_v1_services_proto = out.File
	file_sf_substreams_sink_sql_v1_services_proto_rawDesc = nil
	file_sf_substreams_sink_sql_v1_services_proto_goTypes = nil
	file_sf_substreams_sink_sql_v1_services_proto_depIdxs = nil
}
