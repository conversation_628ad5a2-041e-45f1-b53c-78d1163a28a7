package db

import (
	"context"
	"encoding/json"
	"fmt"
	"math/big"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	// Add regexp for integerRegex
	_ "github.com/go-sql-driver/mysql" // MySQL driver
	"github.com/streamingfast/cli"
	sink "github.com/streamingfast/substreams-sink"
	"go.uber.org/zap"
	"golang.org/x/exp/maps"
)

type mysqlDialect struct{}

// MySQL supports standard transactions.
func (d mysqlDialect) Flush(tx Tx, ctx context.Context, l *Loader, outputModuleHash string, lastFinalBlock uint64) (int, error) {
	var entryCount int
	// MySQL supports multi-table transactions, so we can prepare all inserts within one transaction context.
	// For each table, prepare a single insert statement and then execute it for all entries.
	// No need to commit per table, commit all at the end of the batch.
	entriesPerTable := make(map[string][][]any)
	for entriesPair := l.entries.Oldest(); entriesPair != nil; entriesPair = entriesPair.Next() {
		tableName := entriesPair.Key
		entries := entriesPair.Value

		if l.tracer.Enabled() {
			l.logger.Debug("preparing table entries for flush", zap.String("table_name", tableName), zap.Int("entry_count", entries.Len()))
		}

		currentTableEntries := make([][]any, 0, entries.Len())
		for entryPair := entries.Oldest(); entryPair != nil; entryPair = entryPair.Next() {
			entry := entryPair.Value
			values, err := convertOpToMysqlValues(entry)
			if err != nil {
				return entryCount, fmt.Errorf("failed to get values for entry: %w", err)
			}
			currentTableEntries = append(currentTableEntries, values)
		}
		if len(currentTableEntries) > 0 {
			entriesPerTable[tableName] = currentTableEntries
		}
	}

	// Execute inserts for each table within the single transaction
	for tableName, allValues := range entriesPerTable {
		info := l.tables[tableName]
		columns := make([]string, 0, len(info.columnsByName))
		for column := range info.columnsByName {
			columns = append(columns, EscapeIdentifier(column)) // MySQL identifiers usually quoted with backticks
		}
		sort.Strings(columns)

		// Construct the VALUES part for bulk insert. MySQL supports multi-row insert.
		// However, for Go's database/sql package, we generally prepare a statement for single-row insert
		// and execute it multiple times. For true bulk insert with multiple value rows in a single query,
		// we'd need to construct the query string manually *without* Prepare, which is less safe.
		// Let's stick with Prepare/Exec for simplicity and safety, it implies rows are inserted one by one, but in a tx.
		// A proper bulk insert (INSERT INTO ... VALUES (), (), ()) would require more complex query building.
		// For now, Clickhouse approach (prepare and exec in loop) within a single transaction is sufficient.
		query := fmt.Sprintf(
			"INSERT INTO %s.%s (%s) VALUES (%s)",
			EscapeIdentifier(l.schema),
			EscapeIdentifier(tableName),
			strings.Join(columns, ","),
			strings.Join(strings.Split(strings.Repeat("?", len(columns)), ""), ","), // Placeholder for each column
		)

		stmt, err := tx.Prepare(query)
		if err != nil {
			return entryCount, fmt.Errorf("failed to prepare insert into %q: %w", tableName, err)
		}
		defer stmt.Close()

		for _, values := range allValues {
			if _, err := stmt.ExecContext(ctx, values...); err != nil {
				return entryCount, fmt.Errorf("executing for table %q, values %q: %w", tableName, values, err)
			}
			entryCount++
		}
	}

	return entryCount, nil
}

func (d mysqlDialect) Revert(tx Tx, ctx context.Context, l *Loader, lastValidFinalBlock uint64) error {
	// MySQL supports standard transactions and row deletion.
	// This ensures consistency by removing data beyond the last valid block.
	l.logger.Info("reverting changes for block", zap.Uint64("last_valid_final_block", lastValidFinalBlock))

	// Iterate through all tables and delete rows where block_num > lastValidFinalBlock
	// Assumes all tables have a block_num column
	for tableName, table := range l.tables {
		// Skip cursor table for this operation, it's handled separately.
		if tableName == CURSORS_TABLE {
			continue
		}

		deleteQuery := fmt.Sprintf("DELETE FROM %s.%s WHERE block_num > ?", EscapeIdentifier(l.schema), EscapeIdentifier(tableName))
		if l.tracer.Enabled() {
			l.logger.Debug("executing revert delete", zap.String("query", deleteQuery), zap.Uint64("block_num", lastValidFinalBlock))
		}
		if _, err := tx.ExecContext(ctx, deleteQuery, lastValidFinalBlock); err != nil {
			return fmt.Errorf("revert delete for table %q failed: %w", tableName, err)
		}
	}

	// Delete from the cursors table. The `HISTORY_TABLE` is for reorg, we don't 'update' cursor in revert.
	deleteCursorQuery := fmt.Sprintf("DELETE FROM %s.%s WHERE block_num > ?", EscapeIdentifier(l.schema), EscapeIdentifier(CURSORS_TABLE))
	if l.tracer.Enabled() {
		l.logger.Debug("executing revert cursor delete", zap.String("query", deleteCursorQuery), zap.Uint64("block_num", lastValidFinalBlock))
	}
	if _, err := tx.ExecContext(ctx, deleteCursorQuery, lastValidFinalBlock); err != nil {
		return fmt.Errorf("revert cursor delete failed: %w", err)
	}

	// This method does not commit the transaction, as the caller (Loader.Flush) will eventually commit/rollback.
	return nil
}

func (d mysqlDialect) GetCreateCursorQuery(schema string, withPostgraphile bool) string {
	_ = withPostgraphile // Consider if Postgraphile specific options apply to MySQL
	return fmt.Sprintf(cli.Dedent(`
	CREATE TABLE IF NOT EXISTS %s.%s
	(
    id         VARCHAR(255) PRIMARY KEY,
		cursor     TEXT,
		block_num  BIGINT UNSIGNED,
		block_id   VARCHAR(255)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
	`), EscapeIdentifier(schema), EscapeIdentifier(CURSORS_TABLE))
}

func (d mysqlDialect) GetCreateHistoryQuery(schema string, withPostgraphile bool) string {
	_ = withPostgraphile // Consider postgraphile on MySQL
	return fmt.Sprintf(cli.Dedent(`
	CREATE TABLE IF NOT EXISTS %s.%s
	(
		block_num BIGINT UNSIGNED,
		block_id VARCHAR(255),
		PRIMARY KEY (block_num, block_id)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
	`), EscapeIdentifier(schema), EscapeIdentifier(HISTORY_TABLE))
}

func (d mysqlDialect) ExecuteSetupScript(ctx context.Context, l *Loader, schemaSql string) error {
	// MySQL allows multiple statements in one query if "multiStatements=true" is in DSN,
	// but it's generally safer to split them for error handling.
	for _, query := range strings.Split(schemaSql, ";") {
		cleanedQuery := strings.TrimSpace(query)
		if len(cleanedQuery) == 0 {
			continue
		}
		if _, err := l.ExecContext(ctx, cleanedQuery); err != nil {
			return fmt.Errorf("exec schema: %w", err)
		}
	}
	return nil
}

func (d mysqlDialect) GetUpdateCursorQuery(table, moduleHash string, cursor *sink.Cursor, block_num uint64, block_id string) string {
	// MySQL uses INSERT ... ON DUPLICATE KEY UPDATE for upsert
	return fmt.Sprintf(`
		INSERT INTO %s (id, cursor, block_num, block_id) VALUES ('%s', '%s', %d, '%s')
		ON DUPLICATE KEY UPDATE cursor=VALUES(cursor), block_num=VALUES(block_num), block_id=VALUES(block_id)
	`, EscapeIdentifier(table), moduleHash, cursor, block_num, block_id)
}

func (d mysqlDialect) GetAllCursorsQuery(table string) string {
	return fmt.Sprintf("SELECT id, cursor, block_num, block_id FROM %s", EscapeIdentifier(table))
}

func (d mysqlDialect) ParseDatetimeNormalization(value string) string {
	// MySQL's equivalent for parsing datetime: STR_TO_DATE or FROM_UNIXTIME if it's a Unix timestamp.
	// Assuming value could be 'YYYY-MM-DD HH:MM:SS' or Unix timestamp.
	// For now, let's assume 'YYYY-MM-DD HH:MM:SS' format.
	// This might need refinement based on exact expectations for 'value'.
	return fmt.Sprintf("STR_TO_DATE(%s, '%%Y-%%m-%%d %%H:%%i:%%s')", escapeStringValue(value))
}

func (d mysqlDialect) DriverSupportRowsAffected() bool {
	return true // MySQL driver typically supports RowsAffected
}

func (d mysqlDialect) OnlyInserts() bool {
	return false // MySQL supports DELETE and UPDATE as part of reorgs
}

func (d mysqlDialect) AllowPkDuplicates() bool {
	return false // MySQL does not allow PK duplicates unless using ON DUPLICATE KEY UPDATE
}

func (d mysqlDialect) CreateUser(tx Tx, ctx context.Context, l *Loader, username string, password string, _database string, readOnly bool) error {
	user := EscapeIdentifier(username)  // user names are identifiers
	pass := escapeStringValue(password) // passwords are string values, should be quoted

	// MySQL specific syntax for CREATE USER IF NOT EXISTS and ALTER USER ... IDENTIFIED BY
	// Note: Authentication plugin is important, 'mysql_native_password' is common but 'caching_sha2_password' is default for newer MySQL.
	// We'll use plaintext for simplicity as in Clickhouse dialect, but in reality stronger methods might be preferred.
	//
	// `CREATE USER IF NOT EXISTS` requires MySQL 8.0+. For older versions, check IF NOT EXISTS manually or handle error.
	// Using `CREATE USER...IDENTIFIED WITH mysql_native_password BY` and then `ALTER USER` might be safer.
	// Let's assume MySQL 8.0+ for `IF NOT EXISTS` for now for simplicity, matching Clickhouse.
	createUserQ := fmt.Sprintf("CREATE USER IF NOT EXISTS %s IDENTIFIED BY %s;", user, pass)
	_, err := tx.ExecContext(ctx, createUserQ)
	if err != nil {
		return fmt.Errorf("executing create user query %q: %w", createUserQ, err)
	}

	var grantQ string
	// MySQL GRANT syntax
	if readOnly {
		// Grant SELECT on all tables in the specified database or all databases
		if _database != "" {
			grantQ = fmt.Sprintf("GRANT SELECT ON %s.* TO %s;", EscapeIdentifier(_database), user)
		} else {
			grantQ = fmt.Sprintf("GRANT SELECT ON *.* TO %s;", user)
		}
	} else {
		// Grant all privileges
		if _database != "" {
			grantQ = fmt.Sprintf("GRANT ALL PRIVILEGES ON %s.* TO %s;", EscapeIdentifier(_database), user)
		} else {
			grantQ = fmt.Sprintf("GRANT ALL PRIVILEGES ON *.* TO %s;", user)
		}
	}

	_, err = tx.ExecContext(ctx, grantQ)
	if err != nil {
		return fmt.Errorf("executing grant query %q: %w", grantQ, err)
	}

	// For grants to take effect immediately without needing flush privileges.
	// Some versions/setups might need FLUSH PRIVILEGES, but typically not required after GRANT.
	// _, err = tx.ExecContext(ctx, "FLUSH PRIVILEGES;")
	// if err != nil {
	// 	return fmt.Errorf("executing flush privileges: %w", err)
	// }

	return nil
}

func convertOpToMysqlValues(o *Operation) ([]any, error) {
	// This function is largely identical to convertOpToClickhouseValues
	// as it just extracts and orders values based on schema.
	columns := make([]string, len(o.data))
	i := 0
	for column := range o.data {
		columns[i] = column
		i++
	}
	sort.Strings(columns)
	values := make([]any, len(o.data))
	for i, v := range columns {
		if col, exists := o.table.columnsByName[v]; exists {
			// Use convertToMysqlType specific to MySQL
			convertedType, err := convertToMysqlType(o.data[v], col.scanType)
			if err != nil {
				return nil, fmt.Errorf("converting value %q to type %q in column %q: %w", o.data[v], col.scanType, v, err)
			}
			values[i] = convertedType
		} else {
			return nil, fmt.Errorf("cannot find column %q for table %q (valid columns are %q)", v, o.table.identifier, strings.Join(maps.Keys(o.table.columnsByName), ", "))
		}
	}
	return values, nil
}

func convertToMysqlType(value string, valueType reflect.Type) (any, error) {
	// This function needs adaptation for MySQL specific type handling, especially for time and boolean.
	switch valueType.Kind() {
	case reflect.String:
		return value, nil
	case reflect.Slice:
		// MySQL supports JSON types, so arrays of JSONable structs might be passed as JSON strings
		// For now, mirroring Clickhouse's behavior for slices (JSON unmarshalling).
		res := reflect.New(reflect.SliceOf(valueType.Elem()))
		if err := json.Unmarshal([]byte(value), res.Interface()); err != nil {
			return "", fmt.Errorf("could not JSON unmarshal slice value %q: %w", value, err)
		}
		return string(res.Elem().Bytes()), nil // Return as JSON string
	case reflect.Bool:
		// MySQL typically uses TINYINT(1) for boolean. Go's sql driver should handle bool to TINYINT.
		return strconv.ParseBool(value)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return strconv.ParseInt(value, 10, valueType.Bits())
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return strconv.ParseUint(value, 10, valueType.Bits())
	case reflect.Float32, reflect.Float64:
		return strconv.ParseFloat(value, valueType.Bits())
	case reflect.Struct:
		if valueType == reflectTypeTime {
			// MySQL DATETIME/TIMESTAMP expects 'YYYY-MM-DD HH:MM:SS' format.
			// If input is Unix timestamp as string, convert it. Otherwise, parse string.
			if integerRegex.MatchString(value) {
				// Assuming it's a Unix timestamp (seconds)
				i, err := strconv.ParseInt(value, 10, 64)
				if err != nil {
					return nil, fmt.Errorf("could not convert %s to int64 for time: %w", value, err)
				}
				return time.Unix(i, 0), nil // Return as time.Time
			}

			// Attempt to parse various date-time string formats
			var v time.Time
			var err error
			// Common formats: "2006-01-02T15:04:05Z", "2006-01-02 15:04:05", "2006-01-02"
			possibleFormats := []string{
				time.RFC3339,                 // "2006-01-02T15:04:05Z"
				"2006-01-02 15:04:05",        // Common MySQL DATETIME
				"2006-01-02 15:04:05.999999", // MySQL DATETIME with microseconds
				"2006-01-02",                 // MySQL DATE
				"2006-01-02T15:04:05-07:00",  // With timezone offset
				"2006-01-02T15:04:05",
			}
			for _, format := range possibleFormats {
				v, err = time.Parse(format, value)
				if err == nil {
					// Convert to MySQL native format string 'YYYY-MM-DD HH:MM:SS' or 'YYYY-MM-DD HH:MM:SS.microseconds' if applicable
					// The driver should handle time.Time, so just return the parsed time.Time object.
					return v, nil
				}
			}
			// If none of the formats matched
			return nil, fmt.Errorf("could not convert %s to time.Time, unsupported format", value)
		}
		return nil, fmt.Errorf("unsupported struct type %s", valueType)

	case reflect.Ptr:
		if valueType.String() == "*big.Int" {
			newInt := new(big.Int)
			newInt.SetString(value, 10)
			return newInt, nil
		}

		elemType := valueType.Elem()
		val, err := convertToMysqlType(value, elemType) // Recursive call for pointer's element type
		if err != nil {
			return nil, fmt.Errorf("invalid pointer type during conversion: %w", err)
		}

		// Create a new value of the element type and assign the converted value.
		// Then return a pointer to this new value. This handles cases where the driver expects a pointer to a specific type.
		result := reflect.New(elemType).Elem()
		result.Set(reflect.ValueOf(val))
		return result.Addr().Interface(), nil

	default:
		return value, nil // Fallback, might need specific handling for other types if they arise.
	}
}
