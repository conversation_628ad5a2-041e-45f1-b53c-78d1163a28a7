# MySQL Dialect 实现任务列表

## 核心实现
- [ ] 创建 MySQL dialect 实现文件  
  复制 `db/dialect_clickhouse.go` 为 `db/dialect_mysql.go` 并实现 MySQL 特定逻辑
- [ ] 更新 dialect 注册表  
  在 [`db/dialect.go`](db/dialect.go) 的 `driverDialect` 映射中添加 MySQL 条目
- [ ] 更新 DSN 解析器  
  在 [`db/dsn.go`](db/dsn.go) 的 `dsn.driverMap` 中添加 MySQL 驱动映射

## 开发环境
- [ ] 添加 MySQL 到 Docker Compose  
  更新 [`docker-compose.yml`](docker-compose.yml) 添加 MySQL 服务配置
- [ ] 创建 MySQL 测试 schema  
  在 `devel/` 目录添加 MySQL 的初始化 SQL 脚本

## 文档更新
- [ ] 更新 README  
  在 "Others" 章节添加 MySQL 支持说明，在 "DSN" 章节添加 MySQL DSN 格式
- [ ] 更新 CHANGELOG  
  记录新增 MySQL dialect 支持

## 测试验证
- [ ] 创建 MySQL 测试配置  
  在 `docs/tutorial/` 添加 `substreams.mysql.yaml`
- [ ] 验证端到端流程  
  测试 `setup` 和 `run` 命令在 MySQL 上的执行

## 注意事项
1. 实现时需要参考 MySQL 与 PostgreSQL 的语法差异：
   - 自增字段使用 `AUTO_INCREMENT` 而非 `SERIAL`
   - 字符串类型使用 `VARCHAR` 而非 `TEXT`
   - 需要处理 `DATETIME` 与 `TIMESTAMP` 类型

2. 确保正确处理 MySQL 的保留关键字转义（使用反引号）