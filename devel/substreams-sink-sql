#!/usr/bin/env bash

ROOT="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && cd .. && pwd )"

active_pid=

main() {
  set -e

  version="unknown"
  if [[ -f .version ]]; then
    version=`cat .version`
  fi

  commit=`git rev-list -1 HEAD`
  dirty=
  if [[ ! -z "$(git status --untracked-files=no --porcelain)" ]]; then
    dirty="dirty"
  fi

  pushd "$ROOT" &> /dev/null
    go install -ldflags "-X main.Version=$version" ./cmd/substreams-sink-sql
  popd &> /dev/null

  if [[ $KILL_AFTER != "" ]]; then
    ${GOPATH:-$HOME/go}/bin/substreams-sink-sql "$@" &
    active_pid=$!

    sleep $KILL_AFTER
    kill -s TERM $active_pid &> /dev/null || true
  else
    exec ${GOPATH:-$HOME/go}/bin/substreams-sink-sql "$@"
  fi
}

main "$@"

