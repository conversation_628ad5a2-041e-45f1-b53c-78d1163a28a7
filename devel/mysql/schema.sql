CREATE TABLE IF NOT EXISTS block_meta
(
    id          VARCHAR(255) NOT NULL PRIMARY KEY,
    at          DATETIME,
    number      BIGINT,
    hash        VARCHAR(255),
    parent_hash VARCHAR(255),
    timestamp   DATETIME
);

CREATE TABLE IF NOT EXISTS cursors
(
    id         VARCHAR(255) NOT NULL PRIMARY KEY,
    cursor     TEXT,
    block_num  BIGINT,
    block_id   VARCHAR(255)
);

-- For reorg handling, assuming MySQL dialect will need a history table similar to PostgreSQL
CREATE TABLE IF NOT EXISTS substreams_history
(
    block_num BIGINT UNSIGNED,
    block_id  VARCHAR(255),
    PRIMARY KEY (block_num, block_id)
);