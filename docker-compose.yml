version: "3"

services:
  postgres:
    container_name: postgres-ssp2
    image: postgres:14
    ports:
      - "5432:5432"
    command: ["postgres", "-cshared_preload_libraries=pg_stat_statements"]
    #command: ["postgres", "-cshared_preload_libraries=pg_stat_statements", "-clog_statement=all"]
    environment:
      POSTGRES_USER: dev-node
      POSTGRES_PASSWORD: insecure-change-me-in-prod
      POSTGRES_DB: dev-node
      POSTGRES_INITDB_ARGS: "-E UTF8 --locale=C"
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - ./devel/data/postgres:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "5432"]
      interval: 30s
      timeout: 10s
      retries: 15
  pgweb:
    container_name: pgweb-ssp2
    image: sosedoff/pgweb:0.11.12
    restart: on-failure
    ports:
      - "8081:8081"
    command: ["pgweb", "--bind=0.0.0.0", "--listen=8081", "--binary-codec=hex"]
    links:
      - postgres:postgres
    environment:
      - DATABASE_URL=************************************************************/dev-node?sslmode=disable
    depends_on:
      - postgres
  database:
    container_name: clickhouse-ssp
    image: clickhouse/clickhouse-server:23.9
    user: "101:101"
    hostname: clickhouse
    volumes:
      - ${PWD}/devel/clickhouse-server/config.xml:/etc/clickhouse-server/config.d/config.xml
      - ${PWD}/devel/clickhouse-server/users.xml:/etc/clickhouse-server/users.d/users.xml
    ports:
      - "8123:8123"
      - "9000:9000"
      - "9005:9005"
