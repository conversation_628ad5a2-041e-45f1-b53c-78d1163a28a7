[package]
name = "substreams-postgresql-sink-tutorial"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[dependencies]
substreams = "0.5.0"
substreams-ethereum = "0.8.0"
prost = "0.11"
substreams-database-change = "1.0.0"
anyhow = "1"
prost-types = "0.11"
chrono = { version = "0.4", features = [ "std" ], default-features = false }

[profile.release]
lto = true
opt-level = 's'
strip = "debuginfo"