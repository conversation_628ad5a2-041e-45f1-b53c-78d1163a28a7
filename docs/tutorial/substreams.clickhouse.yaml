specVersion: v0.1.0
package:
  name: 'substreams_postgresql_sink_tutorial'
  version: v0.1.0

protobuf:
  files:
    - block_meta.proto
  importPaths:
    - ./proto

imports:
  sql: https://github.com/streamingfast/substreams-sink-sql/releases/download/protodefs-v1.0.3/substreams-sink-sql-protodefs-v1.0.3.spkg
  blockmeta: https://github.com/streamingfast/substreams-eth-block-meta/releases/download/v0.5.1/substreams-eth-block-meta-v0.5.1.spkg

binaries:
  default:
    type: wasm/rust-v1
    file: target/wasm32-unknown-unknown/release/substreams_postgresql_sink_tutorial.wasm

modules:
  - name: store_block_meta_start
    kind: store
    updatePolicy: set_if_not_exists
    valueType: proto:eth.block_meta.v1.BlockMeta
    inputs:
      - source: sf.ethereum.type.v2.Block

  - name: db_out
    kind: map
    inputs:
      - store: store_block_meta_start
        mode: deltas
    output:
      type: proto:sf.substreams.sink.database.v1.DatabaseChanges

network: mainnet

sink:
  module: db_out
  type: sf.substreams.sink.sql.v1.Service
  config:
    schema: "./schema.clickhouse.sql"
    engine: clickhouse
    postgraphile_frontend:
      enabled: false
    pgweb_frontend:
      enabled: false
